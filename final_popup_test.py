#!/usr/bin/env python3
"""
最终弹窗工具测试脚本
测试优化后的弹窗检测和关闭功能
"""

import sys
import os
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.popup_tool import create_popup_tool
from core.base_driver import driver_manager


def test_optimized_popup_tool():
    """测试优化后的弹窗工具"""
    print("=" * 50)
    print("优化后弹窗工具测试")
    print("=" * 50)
    
    try:
        # 创建弹窗处理工具
        popup_tool = create_popup_tool(driver_manager.driver)
        
        print("\n步骤1: 检测弹窗状态")
        print("-" * 30)
        
        # 检测是否有弹窗
        popup_present = popup_tool.is_popup_present()
        print(f"弹窗检测结果: {'✅ 检测到弹窗' if popup_present else '❌ 未检测到弹窗'}")
        
        if popup_present:
            print("\n步骤2: 尝试关闭弹窗")
            print("-" * 30)
            
            # 尝试关闭弹窗（不启用调试模式，避免卡住）
            print("正在尝试关闭弹窗...")
            success = popup_tool.detect_and_close_popup(timeout=8, debug_mode=False)
            
            if success:
                print("✅ 弹窗关闭成功！")
                
                # 再次检测确认
                time.sleep(1)
                still_present = popup_tool.is_popup_present()
                if still_present:
                    print("⚠️  警告: 检测到弹窗仍然存在")
                else:
                    print("✅ 确认弹窗已完全关闭")
            else:
                print("❌ 弹窗关闭失败")
                
        else:
            print("\n当前没有弹窗需要处理")
            
            # 显示当前屏幕的基本信息
            try:
                device = popup_tool.device
                clickable_count = device(clickable=True).count if device(clickable=True).exists(timeout=1) else 0
                print(f"当前屏幕可点击元素数量: {clickable_count}")
            except:
                print("无法获取屏幕信息")
        
        print("\n步骤3: 测试检测方法")
        print("-" * 30)
        
        # 测试各种检测方法
        device = popup_tool.device
        
        # 检测已知关闭按钮
        close_button_found = False
        for res_id in popup_tool.close_ids[:5]:  # 只检查前5个
            if device(resourceId=res_id).exists(timeout=0.5):
                print(f"✅ 找到关闭按钮: {res_id}")
                close_button_found = True
                break
        
        if not close_button_found:
            print("❌ 未找到已知的关闭按钮")
        
        # 检测弹窗类型
        popup_elements_found = 0
        for class_name in popup_tool.popup_classes[:5]:  # 只检查前5个
            if device(className=class_name).exists(timeout=0.5):
                count = device(className=class_name).count
                print(f"✅ 找到弹窗类型: {class_name} (数量: {count})")
                popup_elements_found += 1
        
        print(f"检测到的弹窗类型元素: {popup_elements_found}")
        
        print("\n" + "=" * 50)
        print("测试完成")
        print("=" * 50)
        
        # 总结
        print("\n测试总结:")
        if popup_present:
            print("- ✅ 弹窗检测功能正常")
            if 'success' in locals() and success:
                print("- ✅ 弹窗关闭功能正常")
            else:
                print("- ❌ 弹窗关闭功能需要进一步优化")
        else:
            print("- ℹ️  当前无弹窗，检测功能正常")
        
        if close_button_found:
            print("- ✅ 关闭按钮检测功能正常")
        else:
            print("- ❌ 关闭按钮检测需要优化")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")


def test_manual_trigger():
    """手动触发弹窗测试"""
    print("\n" + "=" * 50)
    print("手动触发弹窗测试")
    print("=" * 50)
    
    try:
        popup_tool = create_popup_tool(driver_manager.driver)
        device = popup_tool.device
        
        print("请在设备上手动触发一个弹窗（如打开设置、权限请求等）")
        print("然后按回车键继续测试...")
        input()
        
        print("\n检测弹窗...")
        popup_present = popup_tool.is_popup_present()
        print(f"弹窗检测结果: {'✅ 检测到弹窗' if popup_present else '❌ 未检测到弹窗'}")
        
        if popup_present:
            print("尝试关闭弹窗...")
            success = popup_tool.detect_and_close_popup(timeout=10, debug_mode=False)
            print(f"关闭结果: {'✅ 成功' if success else '❌ 失败'}")
        
    except KeyboardInterrupt:
        print("\n用户取消测试")
    except Exception as e:
        print(f"❌ 手动测试失败: {e}")


if __name__ == "__main__":
    print("开始优化后弹窗工具测试...")
    
    # 主要测试
    test_optimized_popup_tool()
    
    # 询问是否进行手动测试
    try:
        print("\n是否进行手动弹窗测试？(y/n): ", end="")
        response = input().strip().lower()
        if response in ['y', 'yes']:
            test_manual_trigger()
    except:
        pass
    
    print("\n所有测试完成！")
