# Core framework package

# 导入主要模块
from .base_driver import BaseDriver
from .base_element import BaseElement
from .base_page import BasePage
from .logger import log, Logger
from .popup_tool import PopupTool, AutomationWithPopupTool, create_popup_tool, create_automation_with_popup

# 导出的公共接口
__all__ = [
    'BaseDriver',
    'BaseElement',
    'BasePage',
    'log',
    'Logger',
    'PopupTool',
    'AutomationWithPopupTool',
    'create_popup_tool',
    'create_automation_with_popup'
]
