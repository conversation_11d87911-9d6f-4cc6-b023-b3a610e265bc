"""
Ella弹窗处理集成测试
测试BaseEllaTest中集成的弹窗处理功能
"""
import pytest
import time
from unittest.mock import Mock, patch, MagicMock
from testcases.test_ella.base_ella_test import BaseEllaTest, SimpleEllaTest


class TestEllaPopupIntegration:
    """Ella弹窗处理集成测试类"""

    def setup_method(self):
        """测试前准备"""
        # 保存原始配置
        self.original_enabled = BaseEllaTest._popup_handling_enabled
        self.original_timeout = BaseEllaTest._popup_timeout
        self.original_interval = BaseEllaTest._popup_check_interval

        self.base_test = BaseEllaTest()
        self.simple_test = SimpleEllaTest()

        # 创建模拟的ella_app
        self.mock_ella_app = Mock()
        self.mock_ella_app.driver = Mock()
        self.mock_ella_app.execute_text_command = Mock(return_value=True)

    def teardown_method(self):
        """测试后清理"""
        # 恢复原始配置
        BaseEllaTest.set_popup_handling_config(
            self.original_enabled, self.original_timeout, self.original_interval
        )

    def test_popup_handling_config_default(self):
        """测试弹窗处理默认配置"""
        assert BaseEllaTest._popup_handling_enabled is True
        assert BaseEllaTest._popup_timeout == 3
        assert BaseEllaTest._popup_check_interval == 0.3

    def test_set_popup_handling_config(self):
        """测试设置弹窗处理配置"""
        # 修改配置
        BaseEllaTest.set_popup_handling_config(
            enabled=False,
            timeout=5,
            check_interval=0.5
        )

        assert BaseEllaTest._popup_handling_enabled is False
        assert BaseEllaTest._popup_timeout == 5
        assert BaseEllaTest._popup_check_interval == 0.5

    @patch('testcases.test_ella.base_ella_test.create_popup_tool')
    def test_get_popup_tool_success(self, mock_create_popup_tool):
        """测试弹窗工具获取成功"""
        mock_popup_tool = Mock()
        mock_create_popup_tool.return_value = mock_popup_tool
        
        # 第一次调用应该创建工具
        result = self.base_test._get_popup_tool(self.mock_ella_app)
        
        assert result == mock_popup_tool
        mock_create_popup_tool.assert_called_once_with(self.mock_ella_app.driver)
        
        # 第二次调用应该返回缓存的工具
        result2 = self.base_test._get_popup_tool(self.mock_ella_app)
        assert result2 == mock_popup_tool
        assert mock_create_popup_tool.call_count == 1  # 不应该再次调用

    @patch('testcases.test_ella.base_ella_test.create_popup_tool')
    def test_get_popup_tool_failure(self, mock_create_popup_tool):
        """测试弹窗工具获取失败"""
        mock_create_popup_tool.side_effect = Exception("创建失败")
        
        result = self.base_test._get_popup_tool(self.mock_ella_app)
        
        assert result is None
        mock_create_popup_tool.assert_called_once_with(self.mock_ella_app.driver)

    def test_handle_popup_after_command_disabled(self):
        """测试弹窗处理禁用时的行为"""
        # 禁用弹窗处理
        BaseEllaTest.set_popup_handling_config(enabled=False)
        
        # 调用弹窗处理方法
        self.base_test._handle_popup_after_command(self.mock_ella_app, "test command")
        
        # 应该没有任何操作
        assert not hasattr(self.base_test, '_popup_tool')
        
        # 恢复配置
        BaseEllaTest.set_popup_handling_config(enabled=True)

    @patch('testcases.test_ella.base_ella_test.create_popup_tool')
    def test_handle_popup_after_command_no_popup(self, mock_create_popup_tool):
        """测试没有弹窗时的处理"""
        mock_popup_tool = Mock()
        mock_popup_tool.is_popup_present.return_value = False
        mock_create_popup_tool.return_value = mock_popup_tool
        
        self.base_test._handle_popup_after_command(self.mock_ella_app, "test command")
        
        mock_popup_tool.is_popup_present.assert_called_once()
        mock_popup_tool.detect_and_close_popup.assert_not_called()

    @patch('testcases.test_ella.base_ella_test.create_popup_tool')
    def test_handle_popup_after_command_with_popup(self, mock_create_popup_tool):
        """测试有弹窗时的处理"""
        mock_popup_tool = Mock()
        mock_popup_tool.is_popup_present.return_value = True
        mock_popup_tool.detect_and_close_popup.return_value = True
        mock_create_popup_tool.return_value = mock_popup_tool
        
        self.base_test._handle_popup_after_command(self.mock_ella_app, "test command")
        
        mock_popup_tool.is_popup_present.assert_called_once()
        mock_popup_tool.detect_and_close_popup.assert_called_once_with(
            timeout=3,
            check_interval=0.3
        )

    @patch('testcases.test_ella.base_ella_test.create_popup_tool')
    def test_handle_popup_after_command_exception(self, mock_create_popup_tool):
        """测试弹窗处理异常时的行为"""
        mock_popup_tool = Mock()
        mock_popup_tool.is_popup_present.side_effect = Exception("检测失败")
        mock_create_popup_tool.return_value = mock_popup_tool
        
        # 异常不应该影响主流程
        try:
            self.base_test._handle_popup_after_command(self.mock_ella_app, "test command")
        except Exception:
            pytest.fail("弹窗处理异常不应该影响主流程")

    @patch('testcases.test_ella.base_ella_test.create_popup_tool')
    def test_execute_command_with_popup_handling(self, mock_create_popup_tool):
        """测试执行命令时的弹窗处理集成"""
        mock_popup_tool = Mock()
        mock_popup_tool.is_popup_present.return_value = True
        mock_popup_tool.detect_and_close_popup.return_value = True
        mock_create_popup_tool.return_value = mock_popup_tool
        
        # 执行命令
        self.base_test._execute_command(self.mock_ella_app, "test command")
        
        # 验证命令执行
        self.mock_ella_app.execute_text_command.assert_called_once_with("test command")
        
        # 验证弹窗处理
        mock_popup_tool.is_popup_present.assert_called_once()
        mock_popup_tool.detect_and_close_popup.assert_called_once()

    def test_simple_test_enable_popup_handling(self):
        """测试SimpleEllaTest启用弹窗处理方法调用"""
        # 测试方法调用不抛出异常
        try:
            self.simple_test.enable_popup_handling(timeout=5, check_interval=0.5)
            # 如果没有异常，测试通过
            assert True
        except Exception as e:
            pytest.fail(f"enable_popup_handling方法调用失败: {e}")

    def test_simple_test_disable_popup_handling(self):
        """测试SimpleEllaTest禁用弹窗处理方法调用"""
        # 测试方法调用不抛出异常
        try:
            self.simple_test.disable_popup_handling()
            # 如果没有异常，测试通过
            assert True
        except Exception as e:
            pytest.fail(f"disable_popup_handling方法调用失败: {e}")

    @patch('testcases.test_ella.base_ella_test.create_popup_tool')
    def test_simple_test_manual_popup_check_with_popup(self, mock_create_popup_tool):
        """测试SimpleEllaTest手动弹窗检查 - 有弹窗"""
        mock_popup_tool = Mock()
        mock_popup_tool.is_popup_present.return_value = True
        mock_popup_tool.detect_and_close_popup.return_value = True
        mock_create_popup_tool.return_value = mock_popup_tool
        
        result = self.simple_test.manual_popup_check(self.mock_ella_app)
        
        assert result is True
        mock_popup_tool.is_popup_present.assert_called_once()
        mock_popup_tool.detect_and_close_popup.assert_called_once()

    @patch('testcases.test_ella.base_ella_test.create_popup_tool')
    def test_simple_test_manual_popup_check_no_popup(self, mock_create_popup_tool):
        """测试SimpleEllaTest手动弹窗检查 - 无弹窗"""
        mock_popup_tool = Mock()
        mock_popup_tool.is_popup_present.return_value = False
        mock_create_popup_tool.return_value = mock_popup_tool
        
        result = self.simple_test.manual_popup_check(self.mock_ella_app)
        
        assert result is False
        mock_popup_tool.is_popup_present.assert_called_once()
        mock_popup_tool.detect_and_close_popup.assert_not_called()

    @patch('testcases.test_ella.base_ella_test.create_popup_tool')
    def test_simple_test_manual_popup_check_exception(self, mock_create_popup_tool):
        """测试SimpleEllaTest手动弹窗检查异常"""
        mock_create_popup_tool.side_effect = Exception("工具创建失败")
        
        result = self.simple_test.manual_popup_check(self.mock_ella_app)
        
        assert result is False

    def test_popup_handling_performance_impact(self):
        """测试弹窗处理对性能的影响"""
        # 这个测试确保弹窗处理不会显著影响性能
        
        # 禁用弹窗处理时的执行时间
        BaseEllaTest.set_popup_handling_config(enabled=False)
        start_time = time.time()
        self.base_test._execute_command(self.mock_ella_app, "test command")
        disabled_time = time.time() - start_time
        
        # 启用弹窗处理时的执行时间
        BaseEllaTest.set_popup_handling_config(enabled=True)
        with patch('testcases.test_ella.base_ella_test.create_popup_tool') as mock_create:
            mock_popup_tool = Mock()
            mock_popup_tool.is_popup_present.return_value = False
            mock_create.return_value = mock_popup_tool
            
            start_time = time.time()
            self.base_test._execute_command(self.mock_ella_app, "test command")
            enabled_time = time.time() - start_time
        
        # 性能影响应该很小（小于100ms）
        performance_impact = enabled_time - disabled_time
        assert performance_impact < 0.1, f"弹窗处理性能影响过大: {performance_impact:.3f}s"

    def test_popup_handling_thread_safety(self):
        """测试弹窗处理的线程安全性"""
        # 确保多次调用不会产生竞态条件
        import threading
        
        results = []
        exceptions = []
        
        def test_thread():
            try:
                with patch('testcases.test_ella.base_ella_test.create_popup_tool') as mock_create:
                    mock_popup_tool = Mock()
                    mock_popup_tool.is_popup_present.return_value = False
                    mock_create.return_value = mock_popup_tool
                    
                    test_instance = BaseEllaTest()
                    test_instance._handle_popup_after_command(self.mock_ella_app, "test")
                    results.append(True)
            except Exception as e:
                exceptions.append(e)
        
        # 创建多个线程同时执行
        threads = [threading.Thread(target=test_thread) for _ in range(5)]
        
        for thread in threads:
            thread.start()
        
        for thread in threads:
            thread.join()
        
        # 验证没有异常且所有线程都成功执行
        assert len(exceptions) == 0, f"线程安全测试失败: {exceptions}"
        assert len(results) == 5, "不是所有线程都成功执行"


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
