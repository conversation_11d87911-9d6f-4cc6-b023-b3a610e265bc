"""
弹窗处理工具测试
测试core/popup_tool.py的功能
"""
import pytest
import time
import uiautomator2 as u2
from unittest.mock import Mock, patch, MagicMock
from core.popup_tool import PopupTool, AutomationWithPopupTool, create_popup_tool, create_automation_with_popup


class TestPopupTool:
    """弹窗处理工具测试类"""

    def setup_method(self):
        """测试前准备"""
        # 创建模拟设备
        self.mock_device = Mock(spec=u2.Device)
        self.mock_device.info = {
            'displayWidth': 1080,
            'displayHeight': 1920
        }
        
        # 创建弹窗工具实例
        self.popup_tool = PopupTool(self.mock_device)

    def test_init_with_device(self):
        """测试使用指定设备初始化"""
        tool = PopupTool(self.mock_device)
        assert tool.device == self.mock_device
        assert len(tool.close_texts) > 0
        assert len(tool.close_ids) > 0
        assert len(tool.popup_classes) > 0

    @patch('uiautomator2.connect')
    def test_init_without_device(self, mock_connect):
        """测试不指定设备时的初始化"""
        mock_device = Mock(spec=u2.Device)
        mock_connect.return_value = mock_device
        
        tool = PopupTool()
        mock_connect.assert_called_once()
        assert tool.device == mock_device

    def test_close_texts_content(self):
        """测试关闭按钮文本内容"""
        expected_texts = ['关闭', '取消', '确定', '同意', 'X', 'Close', 'OK']
        for text in expected_texts:
            assert text in self.popup_tool.close_texts

    def test_close_ids_content(self):
        """测试关闭按钮资源ID内容"""
        expected_ids = [
            'android:id/button1',
            'android:id/button2',
            'com.android.packageinstaller:id/permission_allow_button'
        ]
        for res_id in expected_ids:
            assert res_id in self.popup_tool.close_ids

    def test_popup_classes_content(self):
        """测试弹窗类名内容"""
        expected_classes = [
            'android.app.Dialog',
            'android.app.AlertDialog',
            'android.widget.PopupWindow'
        ]
        for class_name in expected_classes:
            assert class_name in self.popup_tool.popup_classes

    def test_is_close_button_by_position_true(self):
        """测试位置判断关闭按钮 - 正确位置"""
        mock_element = Mock()
        mock_element.info = {
            'bounds': {'right': 900}  # 在右侧80%区域内
        }
        
        result = self.popup_tool._is_close_button_by_position(mock_element)
        assert result is True

    def test_is_close_button_by_position_false(self):
        """测试位置判断关闭按钮 - 错误位置"""
        mock_element = Mock()
        mock_element.info = {
            'bounds': {'right': 500}  # 不在右侧80%区域内
        }
        
        result = self.popup_tool._is_close_button_by_position(mock_element)
        assert result is False

    def test_is_close_button_by_position_no_bounds(self):
        """测试位置判断关闭按钮 - 无边界信息"""
        mock_element = Mock()
        mock_element.info = {}
        
        result = self.popup_tool._is_close_button_by_position(mock_element)
        assert result is False

    def test_try_back_key_success(self):
        """测试返回键操作成功"""
        self.mock_device.press = Mock()
        
        result = self.popup_tool._try_back_key()
        
        self.mock_device.press.assert_called_once_with("back")
        assert result is True

    def test_try_back_key_failure(self):
        """测试返回键操作失败"""
        self.mock_device.press = Mock(side_effect=Exception("按键失败"))
        
        result = self.popup_tool._try_back_key()
        
        assert result is False

    def test_is_popup_present_by_class(self):
        """测试通过类名检测弹窗存在"""
        # 模拟找到Dialog类型的弹窗
        mock_element = Mock()
        mock_element.exists.return_value = True
        self.mock_device.return_value = mock_element
        
        result = self.popup_tool.is_popup_present()
        assert result is True

    def test_is_popup_present_by_keyword(self):
        """测试通过关键词检测弹窗存在"""
        # 模拟所有类名检测都失败
        mock_element_class = Mock()
        mock_element_class.exists.return_value = False
        
        # 模拟关键词检测成功
        mock_element_keyword = Mock()
        mock_element_keyword.exists.return_value = True
        
        def mock_device_call(**kwargs):
            if 'className' in kwargs:
                return mock_element_class
            elif 'textContains' in kwargs:
                return mock_element_keyword
            return Mock()
        
        self.mock_device.side_effect = mock_device_call
        
        result = self.popup_tool.is_popup_present()
        assert result is True

    def test_is_popup_present_false(self):
        """测试检测不到弹窗"""
        mock_element = Mock()
        mock_element.exists.return_value = False
        self.mock_device.return_value = mock_element
        
        result = self.popup_tool.is_popup_present()
        assert result is False

    def test_safe_action_success(self):
        """测试安全操作成功"""
        # 模拟detect_and_close_popup方法
        self.popup_tool.detect_and_close_popup = Mock(return_value=True)
        
        # 定义测试函数
        def test_func(x, y):
            return x + y
        
        result = self.popup_tool.safe_action(test_func, 1, 2)
        
        assert result == 3
        assert self.popup_tool.detect_and_close_popup.call_count == 2

    def test_safe_action_with_exception(self):
        """测试安全操作中出现异常"""
        self.popup_tool.detect_and_close_popup = Mock(return_value=True)
        
        def test_func():
            raise ValueError("测试异常")
        
        with pytest.raises(ValueError, match="测试异常"):
            self.popup_tool.safe_action(test_func)

    def test_click_with_popup_handling_success(self):
        """测试带弹窗处理的点击成功"""
        self.popup_tool.safe_action = Mock(return_value=True)
        
        result = self.popup_tool.click_with_popup_handling(100, 200)
        
        assert result is True
        self.popup_tool.safe_action.assert_called_once()

    def test_click_with_popup_handling_failure(self):
        """测试带弹窗处理的点击失败"""
        self.popup_tool.safe_action = Mock(side_effect=Exception("点击失败"))
        
        result = self.popup_tool.click_with_popup_handling(100, 200)
        
        assert result is False

    def test_input_with_popup_handling_success(self):
        """测试带弹窗处理的输入成功"""
        self.popup_tool.safe_action = Mock(return_value=True)
        
        result = self.popup_tool.input_with_popup_handling("测试文本")
        
        assert result is True
        self.popup_tool.safe_action.assert_called_once()

    def test_element_click_with_popup_handling_success(self):
        """测试带弹窗处理的元素点击成功"""
        # 模拟元素存在并点击成功
        mock_element = Mock()
        mock_element.exists.return_value = True
        mock_element.click.return_value = None
        self.mock_device.return_value = mock_element
        
        self.popup_tool.detect_and_close_popup = Mock(return_value=True)
        
        selector = {'resourceId': 'com.example:id/button'}
        result = self.popup_tool.element_click_with_popup_handling(selector)
        
        assert result is True
        mock_element.click.assert_called_once()

    def test_element_click_with_popup_handling_element_not_found(self):
        """测试带弹窗处理的元素点击 - 元素未找到"""
        mock_element = Mock()
        mock_element.exists.return_value = False
        self.mock_device.return_value = mock_element
        
        self.popup_tool.detect_and_close_popup = Mock(return_value=True)
        
        selector = {'resourceId': 'com.example:id/button'}
        result = self.popup_tool.element_click_with_popup_handling(selector)
        
        assert result is False


class TestAutomationWithPopupTool:
    """自动化弹窗处理工具测试类"""

    def setup_method(self):
        """测试前准备"""
        self.mock_device = Mock(spec=u2.Device)
        self.automation = AutomationWithPopupTool(self.mock_device)

    def test_init_with_device(self):
        """测试使用指定设备初始化"""
        automation = AutomationWithPopupTool(self.mock_device)
        assert automation.device == self.mock_device
        assert isinstance(automation.popup_tool, PopupTool)

    @patch('uiautomator2.connect')
    def test_init_without_device(self, mock_connect):
        """测试不指定设备时的初始化"""
        mock_device = Mock(spec=u2.Device)
        mock_connect.return_value = mock_device
        
        automation = AutomationWithPopupTool()
        mock_connect.assert_called_once()
        assert automation.device == mock_device

    def test_click(self):
        """测试点击方法"""
        self.automation.popup_tool.click_with_popup_handling = Mock(return_value=True)
        
        result = self.automation.click(100, 200)
        
        assert result is True
        self.automation.popup_tool.click_with_popup_handling.assert_called_once_with(100, 200)

    def test_input_text(self):
        """测试输入文本方法"""
        self.automation.popup_tool.input_with_popup_handling = Mock(return_value=True)
        
        result = self.automation.input_text("测试文本")
        
        assert result is True
        self.automation.popup_tool.input_with_popup_handling.assert_called_once_with("测试文本")

    def test_click_element(self):
        """测试点击元素方法"""
        self.automation.popup_tool.element_click_with_popup_handling = Mock(return_value=True)
        
        selector = {'resourceId': 'com.example:id/button'}
        result = self.automation.click_element(selector)
        
        assert result is True
        self.automation.popup_tool.element_click_with_popup_handling.assert_called_once_with(selector)

    def test_detect_and_close_popup(self):
        """测试检测并关闭弹窗方法"""
        self.automation.popup_tool.detect_and_close_popup = Mock(return_value=True)
        
        result = self.automation.detect_and_close_popup(10)
        
        assert result is True
        self.automation.popup_tool.detect_and_close_popup.assert_called_once_with(10)

    def test_is_popup_present(self):
        """测试检测弹窗存在方法"""
        self.automation.popup_tool.is_popup_present = Mock(return_value=True)
        
        result = self.automation.is_popup_present()
        
        assert result is True
        self.automation.popup_tool.is_popup_present.assert_called_once()


class TestConvenienceFunctions:
    """便捷函数测试类"""

    @patch('uiautomator2.connect')
    def test_create_popup_tool_without_device(self, mock_connect):
        """测试创建弹窗工具 - 不指定设备"""
        mock_device = Mock(spec=u2.Device)
        mock_connect.return_value = mock_device
        
        tool = create_popup_tool()
        
        assert isinstance(tool, PopupTool)
        mock_connect.assert_called_once()

    def test_create_popup_tool_with_device(self):
        """测试创建弹窗工具 - 指定设备"""
        mock_device = Mock(spec=u2.Device)
        
        tool = create_popup_tool(mock_device)
        
        assert isinstance(tool, PopupTool)
        assert tool.device == mock_device

    @patch('uiautomator2.connect')
    def test_create_automation_with_popup_without_device(self, mock_connect):
        """测试创建自动化工具 - 不指定设备"""
        mock_device = Mock(spec=u2.Device)
        mock_connect.return_value = mock_device
        
        automation = create_automation_with_popup()
        
        assert isinstance(automation, AutomationWithPopupTool)
        mock_connect.assert_called_once()

    def test_create_automation_with_popup_with_device(self):
        """测试创建自动化工具 - 指定设备"""
        mock_device = Mock(spec=u2.Device)
        
        automation = create_automation_with_popup(mock_device)
        
        assert isinstance(automation, AutomationWithPopupTool)
        assert automation.device == mock_device


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
