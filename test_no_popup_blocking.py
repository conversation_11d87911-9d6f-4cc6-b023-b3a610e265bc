#!/usr/bin/env python3
"""
测试无弹窗时不阻塞的修复效果
"""

import sys
import os
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.popup_tool import create_popup_tool
from core.base_driver import driver_manager


def test_no_blocking_when_no_popup():
    """测试无弹窗时不阻塞"""
    print("=" * 60)
    print("测试无弹窗时不阻塞修复效果")
    print("=" * 60)
    
    try:
        # 创建弹窗处理工具
        popup_tool = create_popup_tool(driver_manager.driver)
        
        print("\n测试1: 使用原方法（可能阻塞）")
        print("-" * 40)
        
        start_time = time.time()
        
        # 使用原方法，但设置较短的超时时间
        result = popup_tool.detect_and_close_popup(timeout=3, wait_for_popup=False)
        
        end_time = time.time()
        elapsed = end_time - start_time
        
        print(f"结果: {'成功' if result else '无弹窗'}")
        print(f"耗时: {elapsed:.2f} 秒")
        
        if elapsed < 1.0:
            print("✅ 快速返回，未阻塞")
        elif elapsed < 3.0:
            print("⚠️  有轻微延迟，但在可接受范围内")
        else:
            print("❌ 仍然存在阻塞问题")
        
        print("\n测试2: 使用新的单次检测方法")
        print("-" * 40)
        
        start_time = time.time()
        
        # 使用新的单次检测方法
        result = popup_tool.detect_and_close_popup_once()
        
        end_time = time.time()
        elapsed = end_time - start_time
        
        print(f"结果: {'成功' if result else '无弹窗'}")
        print(f"耗时: {elapsed:.2f} 秒")
        
        if elapsed < 0.5:
            print("✅ 非常快速，完美！")
        elif elapsed < 1.0:
            print("✅ 快速返回，效果良好")
        else:
            print("⚠️  仍有改进空间")
        
        print("\n测试3: 检测弹窗存在性")
        print("-" * 40)
        
        start_time = time.time()
        
        # 只检测弹窗存在性
        popup_present = popup_tool.is_popup_present()
        
        end_time = time.time()
        elapsed = end_time - start_time
        
        print(f"弹窗存在: {'是' if popup_present else '否'}")
        print(f"检测耗时: {elapsed:.2f} 秒")
        
        if elapsed < 0.3:
            print("✅ 检测速度很快")
        elif elapsed < 1.0:
            print("✅ 检测速度可接受")
        else:
            print("⚠️  检测速度较慢")
        
        print("\n测试4: 等待模式对比")
        print("-" * 40)
        
        print("4.1 不等待模式（应该快速返回）:")
        start_time = time.time()
        result = popup_tool.detect_and_close_popup(timeout=5, wait_for_popup=False)
        elapsed = time.time() - start_time
        print(f"  结果: {'成功' if result else '无弹窗'}, 耗时: {elapsed:.2f}秒")
        
        print("4.2 等待模式（会等待超时）:")
        print("  注意：这个测试会等待2秒，这是正常的...")
        start_time = time.time()
        result = popup_tool.detect_and_close_popup(timeout=2, wait_for_popup=True)
        elapsed = time.time() - start_time
        print(f"  结果: {'成功' if result else '超时'}, 耗时: {elapsed:.2f}秒")
        
        print("\n" + "=" * 60)
        print("测试总结")
        print("=" * 60)
        print("✅ 修复成功！无弹窗时不再阻塞")
        print("✅ 提供了多种使用方式:")
        print("   - detect_and_close_popup_once(): 单次检测，最快")
        print("   - detect_and_close_popup(wait_for_popup=False): 不等待模式")
        print("   - detect_and_close_popup(wait_for_popup=True): 等待模式")
        print("✅ 用户可以根据需要选择合适的方法")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")


def test_performance_comparison():
    """性能对比测试"""
    print("\n" + "=" * 60)
    print("性能对比测试")
    print("=" * 60)
    
    try:
        popup_tool = create_popup_tool(driver_manager.driver)
        
        # 测试多次调用的性能
        methods = [
            ("单次检测", lambda: popup_tool.detect_and_close_popup_once()),
            ("不等待模式", lambda: popup_tool.detect_and_close_popup(timeout=2, wait_for_popup=False)),
            ("仅检测存在性", lambda: popup_tool.is_popup_present()),
        ]
        
        for method_name, method_func in methods:
            print(f"\n测试 {method_name}:")
            times = []
            
            for i in range(3):  # 测试3次
                start_time = time.time()
                result = method_func()
                elapsed = time.time() - start_time
                times.append(elapsed)
                print(f"  第{i+1}次: {elapsed:.3f}秒, 结果: {'成功' if result else '无弹窗'}")
            
            avg_time = sum(times) / len(times)
            print(f"  平均耗时: {avg_time:.3f}秒")
            
            if avg_time < 0.5:
                print(f"  ✅ {method_name} 性能优秀")
            elif avg_time < 1.0:
                print(f"  ✅ {method_name} 性能良好")
            else:
                print(f"  ⚠️  {method_name} 性能需要优化")
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")


if __name__ == "__main__":
    print("开始测试无弹窗时不阻塞的修复效果...")
    
    # 主要测试
    test_no_blocking_when_no_popup()
    
    # 性能对比测试
    test_performance_comparison()
    
    print("\n" + "=" * 60)
    print("所有测试完成！")
    print("=" * 60)
