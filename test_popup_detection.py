#!/usr/bin/env python3
"""
弹窗检测优化测试脚本
用于测试和验证优化后的弹窗检测功能
"""

import time
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.popup_tool import create_popup_tool
from core.logger import log
from core.base_driver import driver_manager


def test_popup_detection():
    """测试弹窗检测功能"""
    print("=" * 60)
    print("弹窗检测优化测试")
    print("=" * 60)
    
    try:
        # 创建弹窗处理工具
        popup_tool = create_popup_tool(driver_manager.driver)
        
        print("\n1. 当前屏幕状态检测")
        print("-" * 40)
        
        # 获取当前屏幕调试信息
        debug_info = popup_tool.debug_current_screen()
        
        print(f"屏幕尺寸: {debug_info['screen_info']['width']}x{debug_info['screen_info']['height']}")
        print(f"检测到的弹窗类型元素: {len(debug_info['detected_elements'])}")
        print(f"弹窗指示器: {len(debug_info['popup_indicators'])}")
        print(f"可点击元素总数: {debug_info['clickable_elements'].get('total_count', 0)}")
        print(f"潜在关闭按钮: {len(debug_info['potential_close_buttons'])}")
        
        # 详细输出检测到的元素
        if debug_info['detected_elements']:
            print("\n检测到的弹窗类型元素:")
            for element in debug_info['detected_elements']:
                print(f"  - {element['className']} (数量: {element['count']})")
        
        if debug_info['popup_indicators']:
            print("\n检测到的弹窗指示器:")
            for indicator in debug_info['popup_indicators']:
                print(f"  - 关键词: {indicator['keyword']} (数量: {indicator['count']})")
        
        if debug_info['potential_close_buttons']:
            print("\n检测到的潜在关闭按钮:")
            for button in debug_info['potential_close_buttons']:
                print(f"  - {button['resourceId']}")
        
        print("\n2. 弹窗存在性检测")
        print("-" * 40)
        
        # 检测是否有弹窗
        popup_present = popup_tool.is_popup_present()
        print(f"弹窗检测结果: {'有弹窗' if popup_present else '无弹窗'}")
        
        if popup_present:
            print("\n3. 弹窗处理测试")
            print("-" * 40)
            
            print("尝试关闭弹窗（启用调试模式）...")
            success = popup_tool.detect_and_close_popup(timeout=15, debug_mode=True)
            
            if success:
                print("✅ 弹窗关闭成功")
                
                # 再次检测确认
                time.sleep(1)
                still_present = popup_tool.is_popup_present()
                if still_present:
                    print("⚠️  警告: 弹窗可能未完全关闭")
                else:
                    print("✅ 确认弹窗已完全关闭")
            else:
                print("❌ 弹窗关闭失败")
                
                # 输出失败后的调试信息
                print("\n失败后的屏幕状态:")
                final_debug = popup_tool.debug_current_screen()
                print(f"可点击元素: {final_debug['clickable_elements'].get('total_count', 0)}")
                print(f"弹窗元素: {len(final_debug['detected_elements'])}")
        else:
            print("\n当前没有检测到弹窗，无需处理")
            
            # 即使没有弹窗，也可以测试检测的准确性
            print("\n测试检测准确性...")
            
            # 显示可点击元素的详细信息
            clickable_info = debug_info['clickable_elements']
            if clickable_info.get('total_count', 0) > 0:
                print(f"\n当前屏幕有 {clickable_info['total_count']} 个可点击元素")
                print("前5个可点击元素的信息:")
                for i, element in enumerate(clickable_info.get('sample_info', [])[:5]):
                    print(f"  {i+1}. 类型: {element['className']}")
                    print(f"     文本: '{element['text']}'")
                    print(f"     ID: {element['resourceId']}")
                    print(f"     描述: '{element['contentDescription']}'")
                    bounds = element['bounds']
                    if bounds:
                        print(f"     位置: ({bounds.get('left', 0)}, {bounds.get('top', 0)}) - ({bounds.get('right', 0)}, {bounds.get('bottom', 0)})")
                    print()
        
        print("\n4. 测试总结")
        print("-" * 40)
        print("✅ 弹窗检测优化测试完成")
        print("📊 建议根据上述信息调整检测策略")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")


def test_specific_popup_scenarios():
    """测试特定弹窗场景"""
    print("\n" + "=" * 60)
    print("特定弹窗场景测试")
    print("=" * 60)
    
    try:
        popup_tool = create_popup_tool(driver_manager.driver)
        
        scenarios = [
            {
                'name': '权限请求弹窗',
                'keywords': ['允许', '拒绝', '权限', 'permission'],
                'resource_ids': ['com.android.packageinstaller:id/permission_allow_button']
            },
            {
                'name': '系统对话框',
                'keywords': ['确定', '取消', 'OK', 'Cancel'],
                'resource_ids': ['android:id/button1', 'android:id/button2']
            },
            {
                'name': '应用更新弹窗',
                'keywords': ['更新', '升级', '下载', 'update'],
                'resource_ids': []
            }
        ]
        
        for scenario in scenarios:
            print(f"\n测试场景: {scenario['name']}")
            print("-" * 30)
            
            # 检测关键词
            found_keywords = []
            for keyword in scenario['keywords']:
                try:
                    if popup_tool.device(textContains=keyword).exists(timeout=1.0):
                        found_keywords.append(keyword)
                except:
                    pass
            
            # 检测资源ID
            found_ids = []
            for res_id in scenario['resource_ids']:
                try:
                    if popup_tool.device(resourceId=res_id).exists(timeout=1.0):
                        found_ids.append(res_id)
                except:
                    pass
            
            print(f"找到的关键词: {found_keywords}")
            print(f"找到的资源ID: {found_ids}")
            
            if found_keywords or found_ids:
                print(f"✅ 检测到 {scenario['name']} 相关元素")
            else:
                print(f"ℹ️  未检测到 {scenario['name']} 相关元素")
        
    except Exception as e:
        print(f"❌ 特定场景测试出现错误: {e}")


if __name__ == "__main__":
    print("开始弹窗检测优化测试...")
    
    # 主要测试
    test_popup_detection()
    
    # 特定场景测试
    test_specific_popup_scenarios()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("=" * 60)
