#!/usr/bin/env python3
"""
快速测试无弹窗时不阻塞的修复效果
"""

import sys
import os
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.popup_tool import create_popup_tool
from core.base_driver import driver_manager


def quick_test():
    """快速测试修复效果"""
    print("=" * 50)
    print("快速测试无弹窗时不阻塞修复")
    print("=" * 50)
    
    try:
        # 创建弹窗处理工具
        popup_tool = create_popup_tool(driver_manager.driver)
        
        print("\n1. 测试快速检测")
        print("-" * 30)
        
        start_time = time.time()
        popup_present = popup_tool.is_popup_present(fast_mode=True)
        elapsed = time.time() - start_time
        
        print(f"快速检测结果: {'有弹窗' if popup_present else '无弹窗'}")
        print(f"检测耗时: {elapsed:.3f} 秒")
        
        if elapsed < 0.5:
            print("✅ 快速检测效果很好")
        else:
            print("⚠️  快速检测仍需优化")
        
        print("\n2. 测试单次检测方法")
        print("-" * 30)
        
        start_time = time.time()
        result = popup_tool.detect_and_close_popup_once()
        elapsed = time.time() - start_time
        
        print(f"单次检测结果: {'成功' if result else '无弹窗'}")
        print(f"检测耗时: {elapsed:.3f} 秒")
        
        if elapsed < 1.0:
            print("✅ 单次检测效果很好")
        else:
            print("⚠️  单次检测仍需优化")
        
        print("\n3. 测试不等待模式")
        print("-" * 30)
        
        start_time = time.time()
        result = popup_tool.detect_and_close_popup(timeout=3, wait_for_popup=False)
        elapsed = time.time() - start_time
        
        print(f"不等待模式结果: {'成功' if result else '无弹窗'}")
        print(f"检测耗时: {elapsed:.3f} 秒")
        
        if elapsed < 2.0:
            print("✅ 不等待模式效果很好")
        else:
            print("⚠️  不等待模式仍需优化")
        
        print("\n" + "=" * 50)
        print("测试总结")
        print("=" * 50)
        
        if all([
            elapsed < 2.0,  # 不等待模式快速
            popup_tool.is_popup_present(fast_mode=True) or True  # 快速检测可用
        ]):
            print("✅ 修复成功！无弹窗时不再长时间阻塞")
            print("推荐使用方法:")
            print("  - popup_tool.detect_and_close_popup_once() # 最快")
            print("  - popup_tool.detect_and_close_popup(wait_for_popup=False) # 不等待")
        else:
            print("⚠️  修复部分有效，但仍有改进空间")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")


if __name__ == "__main__":
    print("开始快速测试...")
    quick_test()
    print("\n测试完成！")
