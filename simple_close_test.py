#!/usr/bin/env python3
"""
简单的关闭按钮测试脚本
直接尝试点击已知的关闭按钮
"""

import sys
import os
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.base_driver import driver_manager
from core.logger import log


def test_direct_close():
    """直接测试关闭按钮点击"""
    print("=" * 40)
    print("直接关闭按钮测试")
    print("=" * 40)
    
    try:
        # 获取设备连接
        device = driver_manager.driver
        print(f"设备连接成功: {device.info}")
        
        # 已知的关闭按钮ID
        close_button_id = 'com.transsion.aivoiceassistant:id/close_iv'
        
        print(f"\n检查关闭按钮: {close_button_id}")
        
        # 检查按钮是否存在
        close_button = device(resourceId=close_button_id)
        
        if close_button.exists(timeout=3):
            print("✅ 找到关闭按钮")
            
            # 获取按钮信息
            button_info = close_button.info
            print(f"按钮位置: {button_info.get('bounds', {})}")
            print(f"按钮类型: {button_info.get('className', '')}")
            print(f"是否可点击: {button_info.get('clickable', False)}")
            
            # 尝试点击
            print("\n尝试点击关闭按钮...")
            close_button.click()
            print("✅ 点击完成")
            
            # 等待一下，然后检查按钮是否还存在
            time.sleep(2)
            
            if close_button.exists(timeout=1):
                print("⚠️  按钮仍然存在，可能未成功关闭")
            else:
                print("✅ 按钮已消失，关闭成功")
                
        else:
            print("❌ 未找到关闭按钮")
            
            # 列出当前屏幕的所有可点击元素
            print("\n当前屏幕的可点击元素:")
            clickable_elements = device(clickable=True)
            
            if clickable_elements.exists(timeout=2):
                print(f"找到 {clickable_elements.count} 个可点击元素:")
                
                for i in range(min(clickable_elements.count, 5)):  # 只显示前5个
                    element = clickable_elements[i] if clickable_elements.count > 1 else clickable_elements
                    try:
                        info = element.info
                        print(f"  {i+1}. {info.get('className', '')}")
                        print(f"     资源ID: {info.get('resourceId', '')}")
                        print(f"     文本: '{info.get('text', '')}'")
                        print(f"     位置: {info.get('bounds', {})}")
                        print()
                    except:
                        print(f"  {i+1}. 无法获取元素信息")
            else:
                print("未找到任何可点击元素")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")


def test_alternative_close_methods():
    """测试其他关闭方法"""
    print("\n" + "=" * 40)
    print("测试其他关闭方法")
    print("=" * 40)
    
    try:
        device = driver_manager.driver
        
        # 方法1: 尝试返回键
        print("1. 尝试返回键...")
        device.press("back")
        time.sleep(1)
        print("✅ 返回键已按下")
        
        # 方法2: 尝试点击右上角区域
        print("\n2. 尝试点击右上角区域...")
        screen_width = device.info['displayWidth']
        screen_height = device.info['displayHeight']
        
        # 点击右上角
        x = int(screen_width * 0.9)
        y = int(screen_height * 0.1)
        
        print(f"点击坐标: ({x}, {y})")
        device.click(x, y)
        time.sleep(1)
        print("✅ 右上角点击完成")
        
        # 方法3: 查找包含"关闭"或"X"的元素
        print("\n3. 查找关闭相关文本...")
        close_texts = ['关闭', '取消', 'X', '×', 'Close']
        
        for text in close_texts:
            element = device(textContains=text)
            if element.exists(timeout=1):
                print(f"找到包含'{text}'的元素，尝试点击...")
                element.click()
                time.sleep(1)
                print(f"✅ 点击包含'{text}'的元素完成")
                break
        else:
            print("未找到包含关闭文本的元素")
        
    except Exception as e:
        print(f"❌ 其他方法测试失败: {e}")


if __name__ == "__main__":
    print("开始简单关闭按钮测试...")
    
    # 主要测试
    test_direct_close()
    
    # 其他方法测试
    test_alternative_close_methods()
    
    print("\n" + "=" * 40)
    print("测试完成")
    print("=" * 40)
