#!/usr/bin/env python3
"""
测试弹窗关闭验证修复效果
"""

import sys
import os
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.popup_tool import create_popup_tool
from core.base_driver import driver_manager


def test_verification_fix():
    """测试弹窗关闭验证修复"""
    print("=" * 60)
    print("测试弹窗关闭验证修复效果")
    print("=" * 60)
    
    try:
        # 创建弹窗处理工具
        popup_tool = create_popup_tool(driver_manager.driver)
        
        print("\n1. 测试验证方法本身")
        print("-" * 40)
        
        # 测试验证方法
        start_time = time.time()
        is_closed = popup_tool._verify_popup_closed()
        elapsed = time.time() - start_time
        
        print(f"验证结果: {'弹窗已关闭' if is_closed else '弹窗仍存在'}")
        print(f"验证耗时: {elapsed:.3f} 秒")
        
        print("\n2. 测试单次检测和关闭")
        print("-" * 40)
        
        start_time = time.time()
        result = popup_tool.detect_and_close_popup_once()
        elapsed = time.time() - start_time
        
        print(f"单次检测结果: {'成功关闭' if result else '无弹窗或关闭失败'}")
        print(f"总耗时: {elapsed:.3f} 秒")
        
        print("\n3. 测试完整的检测和关闭流程")
        print("-" * 40)
        
        start_time = time.time()
        result = popup_tool.detect_and_close_popup(timeout=5, wait_for_popup=False, debug_mode=False)
        elapsed = time.time() - start_time
        
        print(f"完整流程结果: {'成功关闭' if result else '无弹窗或关闭失败'}")
        print(f"总耗时: {elapsed:.3f} 秒")
        
        print("\n4. 测试各种检测方法的一致性")
        print("-" * 40)
        
        methods = [
            ("超快速检测", lambda: popup_tool.is_popup_present_ultra_fast()),
            ("快速检测", lambda: popup_tool.is_popup_present(fast_mode=True)),
            ("普通检测", lambda: popup_tool.is_popup_present(fast_mode=False)),
        ]
        
        results = []
        for method_name, method_func in methods:
            try:
                start_time = time.time()
                result = method_func()
                elapsed = time.time() - start_time
                results.append((method_name, result, elapsed))
                print(f"  {method_name}: {'有弹窗' if result else '无弹窗'} ({elapsed:.3f}秒)")
            except Exception as e:
                print(f"  {method_name}: 失败 - {e}")
                results.append((method_name, None, 0))
        
        # 检查一致性
        valid_results = [r[1] for r in results if r[1] is not None]
        if len(set(valid_results)) <= 1:
            print("  ✅ 各种检测方法结果一致")
        else:
            print("  ⚠️  检测方法结果不一致，可能需要调整")
        
        print("\n5. 模拟弹窗关闭场景")
        print("-" * 40)
        
        # 检查是否有已知的关闭按钮
        device = popup_tool.device
        close_button_id = 'com.transsion.aivoiceassistant:id/close_iv'
        
        if device(resourceId=close_button_id).exists(timeout=1):
            print("发现关闭按钮，模拟关闭流程...")
            
            # 记录关闭前状态
            before_close = popup_tool.is_popup_present_ultra_fast()
            print(f"关闭前弹窗状态: {'有弹窗' if before_close else '无弹窗'}")
            
            # 执行关闭
            start_time = time.time()
            success = popup_tool.detect_and_close_popup_once()
            elapsed = time.time() - start_time
            
            print(f"关闭操作结果: {'成功' if success else '失败'}")
            print(f"关闭操作耗时: {elapsed:.3f} 秒")
            
            # 记录关闭后状态
            after_close = popup_tool.is_popup_present_ultra_fast()
            print(f"关闭后弹窗状态: {'有弹窗' if after_close else '无弹窗'}")
            
            if before_close and not after_close:
                print("✅ 弹窗关闭验证正常")
            elif not before_close and not after_close:
                print("ℹ️  本来就没有弹窗")
            else:
                print("⚠️  弹窗关闭验证可能有问题")
        else:
            print("未发现已知的关闭按钮，跳过模拟测试")
        
        print("\n" + "=" * 60)
        print("测试总结")
        print("=" * 60)
        
        print("✅ 弹窗关闭验证修复完成！")
        print("主要改进:")
        print("  1. 新增 _verify_popup_closed() 方法")
        print("  2. 分层验证：超快速 -> 快速 -> 完整")
        print("  3. 适当的等待时间，确保UI更新")
        print("  4. 更可靠的关闭确认机制")
        
        print("\n推荐使用:")
        print("  - popup_tool.detect_and_close_popup_once() # 日常使用")
        print("  - popup_tool.detect_and_close_popup(wait_for_popup=False) # 不等待模式")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")


if __name__ == "__main__":
    print("开始测试弹窗关闭验证修复...")
    test_verification_fix()
    print("\n测试完成！")
