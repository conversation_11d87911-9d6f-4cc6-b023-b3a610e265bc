#!/usr/bin/env python3
"""
最小化测试 - 只测试核心功能
"""

import sys
import os
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.popup_tool import create_popup_tool
from core.base_driver import driver_manager


def minimal_test():
    """最小化测试"""
    print("=" * 40)
    print("最小化弹窗检测测试")
    print("=" * 40)
    
    try:
        # 创建弹窗处理工具
        popup_tool = create_popup_tool(driver_manager.driver)
        device = popup_tool.device
        
        print("\n1. 直接检查已知关闭按钮")
        print("-" * 30)
        
        start_time = time.time()
        
        # 直接检查已知的关闭按钮
        close_button_id = 'com.transsion.aivoiceassistant:id/close_iv'
        has_close_button = device(resourceId=close_button_id).exists(timeout=0.5)
        
        elapsed = time.time() - start_time
        
        print(f"关闭按钮存在: {'是' if has_close_button else '否'}")
        print(f"检测耗时: {elapsed:.3f} 秒")
        
        print("\n2. 检查可点击元素数量")
        print("-" * 30)
        
        start_time = time.time()
        
        clickable_elements = device(clickable=True)
        clickable_count = clickable_elements.count if clickable_elements.exists(timeout=0.5) else 0
        
        elapsed = time.time() - start_time
        
        print(f"可点击元素数量: {clickable_count}")
        print(f"检测耗时: {elapsed:.3f} 秒")
        
        print("\n3. 使用优化后的快速检测")
        print("-" * 30)
        
        start_time = time.time()
        
        # 使用优化后的快速检测
        popup_present = popup_tool.is_popup_present(fast_mode=True)
        
        elapsed = time.time() - start_time
        
        print(f"快速检测结果: {'有弹窗' if popup_present else '无弹窗'}")
        print(f"检测耗时: {elapsed:.3f} 秒")
        
        print("\n4. 使用单次检测方法")
        print("-" * 30)
        
        start_time = time.time()
        
        result = popup_tool.detect_and_close_popup_once()
        
        elapsed = time.time() - start_time
        
        print(f"单次检测结果: {'成功' if result else '无弹窗'}")
        print(f"检测耗时: {elapsed:.3f} 秒")
        
        print("\n" + "=" * 40)
        print("测试总结")
        print("=" * 40)
        
        # 判断修复效果
        if has_close_button and popup_present:
            print("✅ 检测逻辑正确：有关闭按钮且检测到弹窗")
        elif not has_close_button and not popup_present:
            print("✅ 检测逻辑正确：无关闭按钮且未检测到弹窗")
        else:
            print("⚠️  检测逻辑可能需要调整")
        
        # 判断性能
        max_time = max([elapsed for elapsed in [elapsed]])  # 使用最后一次的elapsed
        if max_time < 1.0:
            print("✅ 性能良好：检测时间在1秒内")
        elif max_time < 3.0:
            print("⚠️  性能一般：检测时间在3秒内")
        else:
            print("❌ 性能较差：检测时间超过3秒")
        
        print(f"\n推荐使用方法:")
        print(f"  popup_tool.detect_and_close_popup_once()  # 单次检测")
        print(f"  popup_tool.is_popup_present(fast_mode=True)  # 快速检测")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")


if __name__ == "__main__":
    print("开始最小化测试...")
    minimal_test()
    print("\n测试完成！")
