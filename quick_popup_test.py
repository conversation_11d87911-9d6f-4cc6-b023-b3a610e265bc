#!/usr/bin/env python3
"""
快速弹窗检测测试脚本
用于验证弹窗检测优化的效果
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.popup_tool import create_popup_tool
from core.logger import log
from core.base_driver import driver_manager


def quick_test():
    """快速测试弹窗检测和关闭功能"""
    print("=" * 50)
    print("快速弹窗检测测试")
    print("=" * 50)
    
    try:
        # 创建弹窗处理工具
        popup_tool = create_popup_tool(driver_manager.driver)
        
        print("\n1. 检测弹窗状态")
        print("-" * 30)
        
        # 检测是否有弹窗
        popup_present = popup_tool.is_popup_present()
        print(f"弹窗检测结果: {'✅ 有弹窗' if popup_present else '❌ 无弹窗'}")
        
        if popup_present:
            print("\n2. 尝试关闭弹窗")
            print("-" * 30)
            
            # 尝试关闭弹窗
            print("正在尝试关闭弹窗...")
            success = popup_tool.detect_and_close_popup(timeout=10, debug_mode=True)
            
            if success:
                print("✅ 弹窗关闭成功！")
            else:
                print("❌ 弹窗关闭失败")
                
                # 显示调试信息
                print("\n调试信息:")
                debug_info = popup_tool.debug_current_screen()
                print(f"- 可点击元素: {debug_info['clickable_elements'].get('total_count', 0)}")
                print(f"- 潜在关闭按钮: {len(debug_info['potential_close_buttons'])}")
                
                if debug_info['potential_close_buttons']:
                    print("找到的关闭按钮:")
                    for btn in debug_info['potential_close_buttons']:
                        print(f"  - {btn['resourceId']}")
        else:
            print("\n当前没有弹窗需要处理")
            
            # 显示当前屏幕状态
            debug_info = popup_tool.debug_current_screen()
            print(f"\n当前屏幕状态:")
            print(f"- 可点击元素: {debug_info['clickable_elements'].get('total_count', 0)}")
            print(f"- 检测到的元素类型: {len(debug_info['detected_elements'])}")
        
        print("\n" + "=" * 50)
        print("测试完成")
        print("=" * 50)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")


if __name__ == "__main__":
    quick_test()
