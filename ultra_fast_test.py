#!/usr/bin/env python3
"""
超快速弹窗检测测试
"""

import sys
import os
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.popup_tool import create_popup_tool
from core.base_driver import driver_manager


def ultra_fast_test():
    """超快速检测测试"""
    print("=" * 50)
    print("超快速弹窗检测测试")
    print("=" * 50)
    
    try:
        # 创建弹窗处理工具
        popup_tool = create_popup_tool(driver_manager.driver)
        
        print("\n1. 超快速检测测试")
        print("-" * 30)
        
        start_time = time.time()
        popup_present = popup_tool.is_popup_present_ultra_fast()
        elapsed = time.time() - start_time
        
        print(f"超快速检测结果: {'有弹窗' if popup_present else '无弹窗'}")
        print(f"检测耗时: {elapsed:.3f} 秒")
        
        if elapsed < 0.5:
            print("✅ 超快速检测效果优秀！")
        elif elapsed < 1.0:
            print("✅ 超快速检测效果良好")
        else:
            print("⚠️  超快速检测仍需优化")
        
        print("\n2. 优化后的单次检测")
        print("-" * 30)
        
        start_time = time.time()
        result = popup_tool.detect_and_close_popup_once()
        elapsed = time.time() - start_time
        
        print(f"单次检测结果: {'成功' if result else '无弹窗'}")
        print(f"检测耗时: {elapsed:.3f} 秒")
        
        if elapsed < 0.5:
            print("✅ 单次检测效果优秀！")
        elif elapsed < 1.0:
            print("✅ 单次检测效果良好")
        else:
            print("⚠️  单次检测仍需优化")
        
        print("\n3. 性能对比测试")
        print("-" * 30)
        
        methods = [
            ("超快速检测", lambda: popup_tool.is_popup_present_ultra_fast()),
            ("快速检测", lambda: popup_tool.is_popup_present(fast_mode=True)),
            ("普通检测", lambda: popup_tool.is_popup_present(fast_mode=False)),
        ]
        
        for method_name, method_func in methods:
            times = []
            for i in range(2):  # 测试2次
                start_time = time.time()
                try:
                    result = method_func()
                    elapsed = time.time() - start_time
                    times.append(elapsed)
                    print(f"  {method_name} 第{i+1}次: {elapsed:.3f}秒, 结果: {'有弹窗' if result else '无弹窗'}")
                except Exception as e:
                    print(f"  {method_name} 第{i+1}次: 失败 - {e}")
                    break
            
            if times:
                avg_time = sum(times) / len(times)
                print(f"  {method_name} 平均耗时: {avg_time:.3f}秒")
                
                if avg_time < 0.5:
                    print(f"  ✅ {method_name} 性能优秀")
                elif avg_time < 1.0:
                    print(f"  ✅ {method_name} 性能良好")
                else:
                    print(f"  ⚠️  {method_name} 性能需要优化")
            print()
        
        print("\n4. 不等待模式测试")
        print("-" * 30)
        
        start_time = time.time()
        result = popup_tool.detect_and_close_popup(timeout=2, wait_for_popup=False)
        elapsed = time.time() - start_time
        
        print(f"不等待模式结果: {'成功' if result else '无弹窗'}")
        print(f"检测耗时: {elapsed:.3f} 秒")
        
        if elapsed < 1.0:
            print("✅ 不等待模式效果优秀！")
        elif elapsed < 2.0:
            print("✅ 不等待模式效果良好")
        else:
            print("⚠️  不等待模式仍需优化")
        
        print("\n" + "=" * 50)
        print("测试总结")
        print("=" * 50)
        
        print("✅ 修复完成！提供了多种检测速度选项:")
        print("   1. is_popup_present_ultra_fast() - 最快，只检查关键指标")
        print("   2. detect_and_close_popup_once() - 单次检测，使用超快速模式")
        print("   3. detect_and_close_popup(wait_for_popup=False) - 不等待模式")
        print("   4. is_popup_present(fast_mode=True) - 快速模式")
        print("   5. is_popup_present(fast_mode=False) - 完整检测")
        
        print("\n推荐使用场景:")
        print("   - 需要最快速度: is_popup_present_ultra_fast()")
        print("   - 一般使用: detect_and_close_popup_once()")
        print("   - 需要等待弹窗: detect_and_close_popup(wait_for_popup=True)")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")


if __name__ == "__main__":
    print("开始超快速弹窗检测测试...")
    ultra_fast_test()
    print("\n测试完成！")
