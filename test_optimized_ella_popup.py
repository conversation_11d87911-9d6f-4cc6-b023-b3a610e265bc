#!/usr/bin/env python3
"""
测试优化后的Ella弹窗处理逻辑
"""

import sys
import os
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from testcases.test_ella.base_ella_test import BaseEllaTest, SimpleEllaTest
from pages.apps.ella.dialogue_page import EllaDialoguePage
from core.logger import log


def test_optimized_popup_handling():
    """测试优化后的弹窗处理功能"""
    print("=" * 60)
    print("测试优化后的Ella弹窗处理逻辑")
    print("=" * 60)
    
    try:
        # 创建测试实例
        base_test = BaseEllaTest()
        simple_test = SimpleEllaTest()
        
        # 创建Ella页面实例
        ella_page = EllaDialoguePage()
        
        print("\n1. 测试弹窗工具初始化")
        print("-" * 40)
        
        # 启动应用
        if ella_page.start_app():
            print("✅ Ella应用启动成功")
            
            # 测试弹窗工具初始化
            popup_tool = base_test._get_popup_tool(ella_page)
            if popup_tool:
                print("✅ 弹窗工具初始化成功")
            else:
                print("❌ 弹窗工具初始化失败")
                return
        else:
            print("❌ Ella应用启动失败")
            return
        
        print("\n2. 测试超快速弹窗检测")
        print("-" * 40)
        
        start_time = time.time()
        has_popup = base_test.ultra_fast_popup_check(ella_page)
        elapsed = time.time() - start_time
        
        print(f"超快速检测结果: {'有弹窗' if has_popup else '无弹窗'}")
        print(f"检测耗时: {elapsed:.3f} 秒")
        
        if elapsed < 1.0:
            print("✅ 超快速检测性能优秀")
        else:
            print("⚠️  超快速检测性能需要优化")
        
        print("\n3. 测试手动弹窗检查（优化版）")
        print("-" * 40)
        
        start_time = time.time()
        manual_result = base_test.manual_popup_check(ella_page)
        elapsed = time.time() - start_time
        
        print(f"手动检查结果: {'处理了弹窗' if manual_result else '无弹窗需要处理'}")
        print(f"检查耗时: {elapsed:.3f} 秒")
        
        if elapsed < 2.0:
            print("✅ 手动检查性能优秀")
        else:
            print("⚠️  手动检查性能需要优化")
        
        print("\n4. 测试智能弹窗处理")
        print("-" * 40)
        
        # 测试快速模式
        start_time = time.time()
        smart_result_fast = base_test.smart_popup_handling(ella_page, "测试命令", wait_for_popup=False)
        elapsed_fast = time.time() - start_time
        
        print(f"智能处理（快速模式）结果: {'处理了弹窗' if smart_result_fast else '无弹窗需要处理'}")
        print(f"快速模式耗时: {elapsed_fast:.3f} 秒")
        
        print("\n5. 测试SimpleEllaTest的快速弹窗检查")
        print("-" * 40)
        
        start_time = time.time()
        quick_result = simple_test.quick_popup_check_and_close(ella_page)
        elapsed_quick = time.time() - start_time
        
        print(f"快速检查和关闭结果: {'处理了弹窗' if quick_result else '无弹窗需要处理'}")
        print(f"快速检查耗时: {elapsed_quick:.3f} 秒")
        
        print("\n6. 测试弹窗处理配置")
        print("-" * 40)
        
        # 测试启用/禁用弹窗处理
        simple_test.enable_popup_handling(timeout=2, check_interval=0.2)
        print("✅ 弹窗处理已启用")
        
        simple_test.disable_popup_handling()
        print("✅ 弹窗处理已禁用")
        
        # 重新启用
        simple_test.enable_popup_handling()
        print("✅ 弹窗处理重新启用")
        
        print("\n7. 测试命令执行后的弹窗处理")
        print("-" * 40)
        
        # 模拟命令执行后的弹窗处理
        test_command = "open bluetooth"
        start_time = time.time()
        base_test._handle_popup_after_command(ella_page, test_command)
        elapsed_command = time.time() - start_time
        
        print(f"命令后弹窗处理完成: {test_command}")
        print(f"处理耗时: {elapsed_command:.3f} 秒")
        
        print("\n" + "=" * 60)
        print("测试总结")
        print("=" * 60)
        
        print("✅ Ella弹窗处理逻辑优化完成！")
        print("主要改进:")
        print("  1. 使用 detect_and_close_popup_once() 替代传统方法")
        print("  2. 新增超快速检测方法 ultra_fast_popup_check()")
        print("  3. 新增智能弹窗处理方法 smart_popup_handling()")
        print("  4. 新增快速检查和关闭方法 quick_popup_check_and_close()")
        print("  5. 优化了性能，减少了阻塞时间")
        
        print("\n性能对比:")
        print(f"  - 超快速检测: {elapsed:.3f}秒")
        print(f"  - 手动检查: {elapsed:.3f}秒")
        print(f"  - 智能处理: {elapsed_fast:.3f}秒")
        print(f"  - 快速检查: {elapsed_quick:.3f}秒")
        print(f"  - 命令后处理: {elapsed_command:.3f}秒")
        
        avg_time = (elapsed + elapsed_fast + elapsed_quick + elapsed_command) / 4
        if avg_time < 1.0:
            print(f"✅ 平均性能优秀: {avg_time:.3f}秒")
        elif avg_time < 2.0:
            print(f"✅ 平均性能良好: {avg_time:.3f}秒")
        else:
            print(f"⚠️  平均性能需要进一步优化: {avg_time:.3f}秒")
        
        print("\n推荐使用方法:")
        print("  - base_test.manual_popup_check(ella_app)  # 手动检查")
        print("  - simple_test.quick_popup_check_and_close(ella_app)  # 快速检查")
        print("  - base_test.smart_popup_handling(ella_app, cmd)  # 智能处理")
        print("  - base_test.ultra_fast_popup_check(ella_app)  # 仅检测")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")
    
    finally:
        # 清理
        try:
            ella_page.stop_app()
            print("\n✅ 应用已停止")
        except:
            pass


if __name__ == "__main__":
    print("开始测试优化后的Ella弹窗处理逻辑...")
    test_optimized_popup_handling()
    print("\n测试完成！")
